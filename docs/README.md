# Software Guidebook

We use the `Software Guidebook` approach with the `C4 model` diagrams from <PERSON> to document our software systems.

## Index

* [01 Context](./01-Context/README.md)
* [02 Functional Overview](./02-Functional%20Overview/README.md)
* [03 Quality Attributes](./03-Quality%20Attributes/README.md)
* [04 Constraints](./04-Constraints/README.md)
* [05 Principals](./05-Principles/README.md)
* [06 Software Architecture](./06-Software%20Architecture/README.md)
* [07 Code](./07-Code/README.md)
* [08 Data](./08-Data/README.md)
* [09 Infrastructure Architecture](./09-Infrastructure%20Architecture/README.md)
* [10 Deployment](./10-Deployment/README.md)
* [11 Operation and Support](./11-Operation%20and%20Support/README.md)
* [12 Development Environment](./12-Development%20Environment/README.md)
* [13 Decision Log](./13-Decision%20Log/README.md)
