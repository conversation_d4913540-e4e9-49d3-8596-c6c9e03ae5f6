{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  extends: [
    ":semanticPrefixFixDepsChoreOthers",
    ":pinDevDependencies",
    ":pinDependencies",
    "group:monorepos",
    "group:recommended",
  ],
  minimumReleaseAge: "8 days",
  osvVulnerabilityAlerts: true,
  semanticCommits: "enabled",
  labels: ["renovate"],
  branchPrefix: "renovate/",
  suppressNotifications: [],
  lockFileMaintenance: {
    enabled: true,
    branchTopic: "lock-file-maintenance-{{manager}}",
    commitMessageExtra: "({{manager}})"
  },
  schedule: ["every weekend"],
  updateNotScheduled: false,
  timezone: "Europe/Zurich",
  vulnerabilityAlerts: {
    groupName: null,
    schedule: [],
    commitMessageSuffix: "[SECURITY]",
    addLabels: ["security"],
    minimumReleaseAge: null,
  },
  // Separate potentially breaking updates, group others
  separateMultipleMajor: false,
  separateMajorMinor: true, // default
  separateMinorPatch: false, // default

  // Pull Request settings
  autoApprove: true,
  automerge: true,
  automergeType: "pr",
  automergeStrategy: "auto",
  platformAutomerge: true,
  prHourlyLimit: 0,
  prConcurrentLimit: 40,

  "packageRules": [
    {
      matchDatasources: ["terraform-provider"],
      matchUpdateTypes: ["patch"],
      minimumReleaseAge: "1 day",
    },

    // Disabled
    {
      matchDepTypes: ["dotnet-sdk"],
      enabled: false
    },
    {
      matchPackagePrefixes: ["SNF.", "app.terraform.io/SNSF-Portal"],
      minimumReleaseAge: null
    }
  ],
}
