import docs from './status-badge.md?raw';
import { html } from 'lit-html';
import { Meta, StoryFn, StoryObj, WebComponentsRenderer } from '@storybook/web-components';
import { unsafeHTML } from 'lit-html/directives/unsafe-html.js';
import { withActions } from '@storybook/addon-actions/decorator';
import { ifDefined } from "lit-html/directives/if-defined.js";
import StatusBadge, { badgeVariants, colorSchemes } from "./status-badge.component";
import './status-badge.component';

const meta: Meta<StatusBadge> = {
  title: 'Components/Status Badge',
  component: 'snf-status-badge',
  argTypes: {
    color: { control: 'select', options: colorSchemes },
    disabled: { control: 'boolean' },
    variant: { control: 'select', options: badgeVariants },
    chevron: { control: 'boolean' },
    open: { control: 'boolean' },
    hasDot: { control: 'boolean' },
  },
  args: {
    hasDot: true,
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
    actions: {
      handles: [ 'click' ],
    },
  },
  decorators: [ withActions<WebComponentsRenderer> ],
};
export default meta;

const Template: StoryFn<StatusBadge> = ({
                                      color,
                                      disabled,
                                      variant,
                                      chevron,
                                      open,
                                      hasDot,
                                      slot
                                   }) => html`
  <snf-status-badge
    color=${ifDefined(color)}
    variant=${ifDefined(variant)}
    ?disabled=${disabled}
    ?chevron=${chevron}
    ?open=${open}
    ?hasDot=${hasDot}
  >
    ${unsafeHTML(slot)}
  </snf-status-badge>
`;

export const InVerification: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'blue',
    slot: 'In Verification',
    disabled: false,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const InPreparation: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'yellow',
    slot: 'In preparation',
    disabled: false,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const Submitted: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'green',
    slot: 'Submitted',
    disabled: false,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const InEvaluation: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'purple',
    slot: 'In evaluation',
    disabled: false,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const NonConsideration: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'red',
    slot: 'Non-consideration',
    disabled: false,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const Disabled: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'grey',
    slot: 'Disabled',
    disabled: true,
    variant: 'outlined',
    chevron: false,
    open: false,
  },
};

export const InList: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'green',
    slot: 'Approved',
    disabled: false,
    variant: 'flat',
    chevron: false,
    open: false,
  },
};

export const ChevronEnabled: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'yellow',
    slot: 'Draft',
    disabled: false,
    variant: 'outlined',
    chevron: true,
    open: false,
  },
};

export const ChevronDisabled: StoryObj<StatusBadge> = {
  render: Template,
  args: {
    color: 'grey',
    slot: 'Closed',
    disabled: true,
    variant: 'outlined',
    chevron: true,
    open: false,
  },
};
