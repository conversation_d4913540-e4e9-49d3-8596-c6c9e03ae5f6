import "./status-badge.component";
import { ColorScheme, BadgeVariant } from "./status-badge.component";
import "../../icon/icon.component";
import { describe, expect, test, vi } from "vitest";
import { elementUpdated, fixture, html } from "@open-wc/testing-helpers";
import userEvent from '@testing-library/user-event';


describe("StatusBadge", () => {
  test("should display default statusbadge with no content when no slot is given", async () => {
    const element: HTMLElementTagNameMap["snf-status-badge"] = await fixture(
      html` <snf-status-badge></snf-status-badge> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      element.shadowRoot
        ?.querySelector("slot")
        ?.assignedNodes({ flatten: true }).length,
    ).toBe(0);
    expect(element.color).toBe("green");
    expect(element.disabled).toBe(false);
    expect(element.variant).toBe("outlined");
    expect(element.chevron).toBe(false)
    expect(element.open).toBe(false)

    const dot = element.shadowRoot?.querySelector('.dot');
    expect(dot).not.toBeNull();
  });
  test("should display default statusbadge with content when slot is but no configuration is given", async () => {
    const element: HTMLElementTagNameMap["snf-status-badge"] = await fixture(
      html` <snf-status-badge>Test</snf-status-badge> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      element.shadowRoot
        ?.querySelector("slot")
        ?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(element.color).toBe("green");
    expect(element.disabled).toBe(false);
    expect(element.variant).toBe('outlined');
    expect(element.chevron).toBe(false)
    expect(element.open).toBe(false)
  });

  test.each([
    ["green", "outlined", false, false, false],
    ["green", "outlined", false, false, true],
    ["green", "outlined", false, true, false],
    ["green", "outlined", false, true, true],
    ["green", "outlined", true, false, false],
    ["green", "outlined", true, false, true],
    ["green", "outlined", true, true, false],
    ["green", "outlined", true, true, true],
    ["green", "flat", false, false, false],
    ["green", "flat", false, false, true],
    ["green", "flat", false, true, false],
    ["green", "flat", false, true, true],
    ["green", "flat", true, false, false],
    ["green", "flat", true, false, true],
    ["green", "flat", true, true, false],
    ["green", "flat", true, true, true],
    ["yellow", "outlined", false, false, false],
    ["yellow", "outlined", false, false, true],
    ["yellow", "outlined", false, true, false],
    ["yellow", "outlined", false, true, true],
    ["yellow", "outlined", true, false, false],
    ["yellow", "outlined", true, false, true],
    ["yellow", "outlined", true, true, false],
    ["yellow", "outlined", true, true, true],
    ["yellow", "flat", false, false, false],
    ["yellow", "flat", false, false, true],
    ["yellow", "flat", false, true, false],
    ["yellow", "flat", false, true, true],
    ["yellow", "flat", true, false, false],
    ["yellow", "flat", true, false, true],
    ["yellow", "flat", true, true, false],
    ["yellow", "flat", true, true, true],
    ["blue", "outlined", false, false, false],
    ["blue", "outlined", false, false, true],
    ["blue", "outlined", false, true, false],
    ["blue", "outlined", false, true, true],
    ["blue", "outlined", true, false, false],
    ["blue", "outlined", true, false, true],
    ["blue", "outlined", true, true, false],
    ["blue", "outlined", true, true, true],
    ["blue", "flat", false, false, false],
    ["blue", "flat", false, false, true],
    ["blue", "flat", false, true, false],
    ["blue", "flat", false, true, true],
    ["blue", "flat", true, false, false],
    ["blue", "flat", true, false, true],
    ["blue", "flat", true, true, false],
    ["blue", "flat", true, true, true],
    ["red", "outlined", false, false, false],
    ["red", "outlined", false, false, true],
    ["red", "outlined", false, true, false],
    ["red", "outlined", false, true, true],
    ["red", "outlined", true, false, false],
    ["red", "outlined", true, false, true],
    ["red", "outlined", true, true, false],
    ["red", "outlined", true, true, true],
    ["red", "flat", false, false, false],
    ["red", "flat", false, false, true],
    ["red", "flat", false, true, false],
    ["red", "flat", false, true, true],
    ["red", "flat", true, false, false],
    ["red", "flat", true, false, true],
    ["red", "flat", true, true, false],
    ["red", "flat", true, true, true],
    ["purple", "outlined", false, false, false],
    ["purple", "outlined", false, false, true],
    ["purple", "outlined", false, true, false],
    ["purple", "outlined", false, true, true],
    ["purple", "outlined", true, false, false],
    ["purple", "outlined", true, false, true],
    ["purple", "outlined", true, true, false],
    ["purple", "outlined", true, true, true],
    ["purple", "flat", false, false, false],
    ["purple", "flat", false, false, true],
    ["purple", "flat", false, true, false],
    ["purple", "flat", false, true, true],
    ["purple", "flat", true, false, false],
    ["purple", "flat", true, false, true],
    ["purple", "flat", true, true, false],
    ["purple", "flat", true, true, true],
    ["grey", "outlined", false, false, false],
    ["grey", "outlined", false, false, true],
    ["grey", "outlined", false, true, false],
    ["grey", "outlined", false, true, true],
    ["grey", "outlined", true, false, false],
    ["grey", "outlined", true, false, true],
    ["grey", "outlined", true, true, false],
    ["grey", "outlined", true, true, true],
    ["grey", "flat", false, false, false],
    ["grey", "flat", false, false, true],
    ["grey", "flat", false, true, false],
    ["grey", "flat", false, true, true],
    ["grey", "flat", true, false, false],
    ["grey", "flat", true, false, true],
    ["grey", "flat", true, true, false],
    ["grey", "flat", true, true, true],
  ])("should have correct configuration and classes based on input values ('%s', '%s', '%s', '%s', '%s')", async (color, variant, disabled, chevron, open) => {
    const element: HTMLElementTagNameMap["snf-status-badge"] = await fixture(html`
      <snf-status-badge
        color=${color as ColorScheme}
        variant=${variant as BadgeVariant}
        ?disabled=${disabled}
        ?chevron=${chevron}
        ?open=${open}
        ><span>test</span></snf-status-badge
      >
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(element.color).toBe(color);
    expect(element.variant).toBe(variant);
    expect(element.disabled).toBe(disabled);
    expect(element.chevron).toBe(chevron);
    expect(element.open).toBe(open);

    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain(color);
    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain(variant);
   
    if (disabled) {
      expect(
        element.shadowRoot?.querySelector(".container")?.className,
      ).toContain("disabled");
    } else {
      expect(
        element.shadowRoot?.querySelector(".container")?.className,
      ).toContain("enabled");
    }
    if (chevron) {
      expect(
        element.shadowRoot?.querySelector(".container")?.className,
      ).toContain("chevron");
    } else {
      expect(
        element.shadowRoot?.querySelector(".container")?.className,
      ).not.toContain("chevron");
    }
    if (chevron && open) {
      expect(
        element.shadowRoot?.querySelector("snf-icon")?.innerHTML
      ).toContain("arrow_drop_up");
    } else if (chevron && !open) {
      expect(
        element.shadowRoot?.querySelector("snf-icon")?.innerHTML
      ).toContain("arrow_drop_down");
    } else {
      expect(
        element.shadowRoot
          ?.querySelector("snf-icon")
      ).toBeNull();
    }
    
  });

  test.each([
    [true, true, true],
    [true, true, false],
    [true, false, true],
    [true, false, false],
    [false, true, true],
    [false, true, false],
    [false, false, true],
    [false, false, false],
  ])("should correctly fire 'status-open-toggle' event or suppresses for chevron '%s', open '%s' and disabled '%s'", async (chevron, open, disabled) => {
    const element: HTMLElementTagNameMap["snf-status-badge"] = await fixture(html`
      <snf-status-badge
        ?disabled=${disabled}
        ?chevron=${chevron}
        ?open=${open}
        ><span>test</span></snf-status-badge
      >
    `);
    const mockStatusOpenToggleConnectedCallback = vi.fn(() => true);
    element.addEventListener('status-open-toggle', () => {
      mockStatusOpenToggleConnectedCallback();
    });

    const div = element.shadowRoot?.querySelector('.container');
    const user = userEvent.setup();
    await user.click(div!);
    if (chevron && !disabled) {
      expect(element.open).toBe(!open);
      expect(mockStatusOpenToggleConnectedCallback).toHaveBeenCalledTimes(1);
    }
    else {
      expect(element.open).toBe(open);
      expect(mockStatusOpenToggleConnectedCallback).toHaveBeenCalledTimes(0);
    }

  });

  test("should display statusbadge with no dot", async () => {
     const element: HTMLElementTagNameMap["snf-status-badge"] = await fixture(html`
        <snf-status-badge
          ?hasDot=${false}
          ><span>test</span></snf-status-badge
        >
      `);

      await elementUpdated(element);
      await vi.dynamicImportSettled();

      const dot = element.shadowRoot?.querySelector('.dot');
      console.log(element.shadowRoot?.innerHTML);
      expect(dot).toBeNull();
    }
  );
});
