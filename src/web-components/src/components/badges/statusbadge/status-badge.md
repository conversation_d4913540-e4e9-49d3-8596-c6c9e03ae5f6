## Usage

There are different uses for the Status-Badge component

- as a normal status label
- as a status label within a list 
- as a status label that opens and closes while dispatching a toggle event 'status-open-toggle'

## Normal Status Label

```html
<snf-status-badge color="blue" variant="outlined">
    In Verification
  </snf-status-badge>
```

## Status Label within a list
```html
<snf-status-badge color="green" variant="flat">
    Approved
  </snf-status-badge>
```

## Status Label with chevron and toggle functionality
```html
<snf-status-badge color="yellow" variant="outlined" chevron="">
    Draft
  </snf-status-badge>
```

  Emits the custom event 'status-open-toggle' with the value of the open property in the payload detail: { open: this.open }.
  This event is only emitted when chevron is set to true and the component is not disabled. The status badge only toggles the open
  property which toggles the displayed chevron. The value of open can also be set as a property from the outside, so that the
  status badge can be closed from the outside.

```html
<snf-status-badge color="grey" variant="outlined" chevron="">
    Closed
  </snf-status-badge>
```

  The disabled component does not emit the event and does not toggle the open property.

