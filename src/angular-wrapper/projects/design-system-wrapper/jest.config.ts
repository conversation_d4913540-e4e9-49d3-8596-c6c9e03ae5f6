import { pathsToModuleNameMapper } from 'ts-jest';

export default {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
  testMatch: ['**/+(*.)+(spec|test).ts'],
  collectCoverage: true,
  coverageReporters: ['html'],
  moduleNameMapper: pathsToModuleNameMapper(
    {},
    {
      prefix: '<rootDir>/',
    }
  ),
  transform: {
    '^.+\\.(ts|mjs|html)$': [
      'jest-preset-angular',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
        stringifyContentPathRegex: '\\.(html|svg)$',
      },
    ],
    '^.+\\.js$': [
      'babel-jest',
      {
        presets: ['@babel/preset-env'],
      },
    ],
  },
  testEnvironment: 'jsdom',
};
