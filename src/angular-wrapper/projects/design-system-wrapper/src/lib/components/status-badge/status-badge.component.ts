import { Component, Input } from '@angular/core';
import {
  ColorScheme,
  BadgeVariant,
} from '@snf/design-system-components/src/components/badges/statusbadge/status-badge.component';
import { ButtonWrapperBase } from '../../utils/button-wrapper';

@Component({
  selector: 'snf-library-status-badge',
  templateUrl: './status-badge.component.html',
  standalone: false,
})
export class StatusBadgeComponent extends ButtonWrapperBase {
  @Input() public color: ColorScheme = 'green';
  @Input() public variant: BadgeVariant = 'outlined';
  @Input() public chevron: boolean = false;
  @Input() public open: boolean = false;
  @Input() public hasDot: boolean = true;
}
