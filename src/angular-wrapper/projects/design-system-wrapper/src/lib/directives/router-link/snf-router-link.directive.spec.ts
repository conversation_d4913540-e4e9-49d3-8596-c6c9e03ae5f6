import { Location } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { By } from '@angular/platform-browser';
import {
  TestComponent,
  TestNavigationComponent,
  TestNavigationWithLinkComponent,
  TestPageComponent,
} from './router-link-test-components';

const childPaths = ['link1', 'link2', 'link3'];

describe('SnfRouterLinkDirective', () => {
  let component: TestComponent;
  let harness: RouterTestingHarness;
  let location: Location;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        provideRouter([
          {
            path: 'test',
            component: TestComponent,
            children: childPaths.map(path => ({
              path,
              component: TestPageComponent,
            })),
          },
        ]),
      ],
    });

    harness = await RouterTestingHarness.create('/test');
    location = TestBed.inject(Location);
  });

  it('should create an instance', () => {
    harness.detectChanges();
    const component = harness.routeDebugElement?.componentInstance as TestComponent | undefined;
    expect(component).toBeTruthy();
    expect(component instanceof TestComponent).toBeTruthy();
  });

  it('should have href property set', () => {
    harness.detectChanges();
    component = harness.routeDebugElement?.componentInstance as TestComponent;
    component.links = childPaths;
    component.noForRenderedLink = 'single-link';
    harness.detectChanges();
    const links = harness.routeDebugElement?.queryAll(By.directive(TestNavigationComponent));
    expect(links).toBeTruthy();
    expect(links?.length).toBe(childPaths.length + 1); // +1 for singleLink

    const components = links?.map(link => link.componentInstance as TestNavigationComponent) ?? [];
    for (let index = 0; index < childPaths.length; index++) {
      const element = components[index];
      expect(element.href).toBe(`/test/${childPaths[index]}`);
    }
    expect(components.filter(c => c.href === '/test/single-link').length).toBe(1);
  });

  it('should navigate to the correct route for one link', () => {
    harness.detectChanges();
    component = harness.routeDebugElement?.componentInstance as TestComponent;
    component.links = childPaths;
    harness.detectChanges();
    const links = harness.routeDebugElement?.queryAll(By.directive(TestNavigationComponent));

    const firstLink = links?.[0];
    firstLink?.nativeElement.click();
    harness.detectChanges();

    expect(location.path()).toBe(`/test/${childPaths[0]}`);
  });

  it('should navigate through the angular routing and not by the anchor', () => {
    harness.detectChanges();
    component = harness.routeDebugElement?.componentInstance as TestComponent;
    component.links = childPaths;
    harness.detectChanges();
    const links = harness.routeDebugElement?.queryAll(
      By.directive(TestNavigationWithLinkComponent)
    );

    const firstLink = links?.[0];
    firstLink?.nativeElement.click();
    harness.detectChanges();

    expect(location.path()).toBe(`/test/${childPaths[0]}`);
  });
});
