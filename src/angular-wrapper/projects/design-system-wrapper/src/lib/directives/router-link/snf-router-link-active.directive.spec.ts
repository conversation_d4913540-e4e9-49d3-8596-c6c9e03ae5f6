import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { By } from '@angular/platform-browser';
import {
  TestComponent,
  TestNavigationComponent,
  TestPageComponent,
} from './router-link-test-components';

const childPaths = ['link1', 'link2', 'link3'];

describe('SnfRouterLinkActiveDirective', () => {
  let harness: RouterTestingHarness;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        provideRouter([
          {
            path: 'test',
            component: TestComponent,
            children: childPaths.map(path => ({
              path,
              component: TestPageComponent,
            })),
          },
        ]),
      ],
    });

    harness = await RouterTestingHarness.create('/test');
  });

  it('should set first link component active when navigating to it', async () => {
    // test will come in next pr
    harness.detectChanges();

    const component = harness.routeDebugElement?.componentInstance as TestComponent;
    component.links = childPaths;
    harness.detectChanges();
    const links = harness.routeDebugElement?.queryAll(By.directive(TestNavigationComponent));

    await harness.navigateByUrl('/test/link1');
    harness.detectChanges();

    const firstLink = links?.[0];
    expect(firstLink?.componentInstance.active).toBe(true);
  });

  it('should set first link component active when clicking on it', () => {
    // test will come in next pr
    harness.detectChanges();

    const component = harness.routeDebugElement?.componentInstance as TestComponent;
    component.links = childPaths;
    harness.detectChanges();
    const links = harness.routeDebugElement?.queryAll(By.directive(TestNavigationComponent));

    const firstLink = links?.[0];
    firstLink?.nativeElement.click();
    harness.detectChanges();

    expect(firstLink?.componentInstance.active).toBe(true);
  });
});
