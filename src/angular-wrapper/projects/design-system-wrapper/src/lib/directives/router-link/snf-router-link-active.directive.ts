import { IsActiveMatchOptions, NavigationEnd, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { filter, from, mergeAll, of } from 'rxjs';
import { ACTIVABLE_COMPONENT_TOKEN, ActivableComponent } from '../../models';
import { SnfRouterLinkDirective } from './snf-router-link.directive';
import {
  AfterContentInit,
  ContentChildren,
  Directive,
  ElementRef,
  Host,
  Inject,
  Input,
  Optional,
  QueryList,
  Renderer2,
} from '@angular/core';

@UntilDestroy()
@Directive({
  selector: '[snfRouterLink][snfRouterLinkActive]',
  exportAs: 'snfRouterLinkActive',
  standalone: true,
})
export class SnfRouterLinkActiveDirective implements AfterContentInit {
  @ContentChildren(SnfRouterLinkDirective, { descendants: true })
  public links?: QueryList<SnfRouterLinkDirective>;

  /**
   * Aria-current attribute to apply when the router link is active.
   *
   * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.
   *
   * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}
   */
  @Input() public ariaCurrentWhenActive?:
    | 'page'
    | 'step'
    | 'location'
    | 'date'
    | 'time'
    | true
    | false;
  private _isActive = false;

  constructor(
    private router: Router,
    private element: ElementRef,
    private renderer: Renderer2,
    @Optional() private link?: SnfRouterLinkDirective,
    @Optional() @Host() @Inject(ACTIVABLE_COMPONENT_TOKEN) private component?: ActivableComponent
  ) {
    router.events
      .pipe(
        filter(s => s instanceof NavigationEnd),
        untilDestroyed(this)
      )
      .subscribe(() => {
        this.update();
      });
  }

  public ngAfterContentInit(): void {
    if (!this.links) {
      return;
    }

    // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).
    of(this.links.changes, of(null))
      .pipe(mergeAll())
      .subscribe(() => {
        this.update();
        this.subscribeToEachLinkOnChanges();
      });
  }

  public get isActive(): boolean {
    return this.hasActiveLinks();
  }

  private update(): void {
    if (!this.links || !this.router.navigated) return;

    queueMicrotask(() => {
      const hasActiveLinks = this.hasActiveLinks();
      this._isActive = hasActiveLinks;

      if (this.component) {
        this.component.active = hasActiveLinks;
      }

      if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {
        this.renderer.setAttribute(
          this.element.nativeElement,
          'aria-current',
          this.ariaCurrentWhenActive.toString()
        );
      } else {
        this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');
      }
    });
  }

  private subscribeToEachLinkOnChanges(): void {
    const allLinkChanges = [...(this.links?.toArray() ?? []), this.link]
      .filter((link): link is SnfRouterLinkDirective => !!link)
      .map(link => link.onChanges);

    from(allLinkChanges)
      .pipe(mergeAll(), untilDestroyed(this))
      .subscribe(link => {
        if (this._isActive !== this.isLinkActive(this.router)(link)) {
          this.update();
        }
      });
  }

  private isLinkActive(router: Router): (link: SnfRouterLinkDirective) => boolean {
    // if necessary, we can add an input to allow the user to specify the exact matching
    const options: IsActiveMatchOptions = {
      fragment: 'ignored',
      paths: 'subset',
      matrixParams: 'ignored',
      queryParams: 'subset',
    };

    return (link: SnfRouterLinkDirective) => {
      const urlTree = link.urlTree;
      return urlTree ? router.isActive(urlTree, options) : false;
    };
  }

  private hasActiveLinks(): boolean {
    const isActiveCheckFn = this.isLinkActive(this.router);
    return (
      ((this.link && isActiveCheckFn(this.link)) || this.links?.some(isActiveCheckFn)) ?? false
    );
  }
}
