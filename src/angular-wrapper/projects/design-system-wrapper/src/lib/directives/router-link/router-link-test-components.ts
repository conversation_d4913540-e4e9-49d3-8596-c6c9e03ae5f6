import { Component, Directive, forwardRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import {
  ACTIVABLE_COMPONENT_TOKEN,
  ActivableComponent,
  NAVIGABLE_COMPONENT_TOKEN,
  NavigableComponent,
} from '../../models';
import { SnfRouterLinkDirective } from './snf-router-link.directive';
import { SnfRouterLinkActiveDirective } from './snf-router-link-active.directive';

/**
 * The purpose if this test extension directive is for changing the selectors of the directive.
 * In real scenario it's only needed to update the selector of the directive.
 */
@Directive({
  selector: `ia-test-navigation-component[snfRouterLink],
             ia-test-navigation-with-link-component[snfRouterLink]`,
  standalone: true,
  providers: [
    {
      provide: SnfRouterLinkDirective,
      useExisting: TestRouterLinkDirective,
    },
  ],
})
export class TestRouterLinkDirective extends SnfRouterLinkDirective {}

@Component({
  selector: 'ia-test-page-component',
  template: `<h1>{{ title }}</h1>`,
  standalone: true,
})
export class TestPageComponent {
  @Input() public title = '';
}

@Component({
  selector: 'ia-test-navigation-component',
  template: `<span>{{ label }}</span>`,
  standalone: true,
  providers: [
    {
      provide: NAVIGABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => TestNavigationComponent),
    },
    {
      provide: ACTIVABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => TestNavigationComponent),
    },
  ],
})
export class TestNavigationComponent implements NavigableComponent, ActivableComponent {
  @Input() public active = false;
  @Input() public href = '#';
  @Input() public label = '';
}

@Component({
  selector: 'ia-test-navigation-with-link-component',
  template: `<a [href]="href">{{ label }}</a>`,
  standalone: true,
  providers: [
    {
      provide: NAVIGABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => TestNavigationWithLinkComponent),
    },
    {
      provide: ACTIVABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => TestNavigationWithLinkComponent),
    },
  ],
})
export class TestNavigationWithLinkComponent implements NavigableComponent {
  @Input() public href = '#';
  @Input() public label = '';
  @Input() public active = false;
}

@Component({
  template: `
    @for (link of links; track link) {
      <ia-test-navigation-component
        [snfRouterLink]="link"
        snfRouterLinkActive
      ></ia-test-navigation-component
      >,
    }
    @for (link of links; track link) {
      <ia-test-navigation-with-link-component
        [snfRouterLink]="link"
        snfRouterLinkActive
      ></ia-test-navigation-with-link-component
      >,
    }
    <ia-test-navigation-component
      [snfRouterLink]="noForRenderedLink"
      snfRouterLinkActive
    ></ia-test-navigation-component>
    <ia-test-navigation-with-link-component
      [snfRouterLink]="noForRenderedLink"
      snfRouterLinkActive
    ></ia-test-navigation-with-link-component>
    <router-outlet></router-outlet>
  `,
  standalone: true,
  imports: [
    CommonModule,
    TestNavigationComponent,
    TestNavigationWithLinkComponent,
    TestRouterLinkDirective,
    RouterOutlet,
    SnfRouterLinkActiveDirective,
  ],
})
export class TestComponent {
  public links: string[] = [];
  public noForRenderedLink = '';
}
