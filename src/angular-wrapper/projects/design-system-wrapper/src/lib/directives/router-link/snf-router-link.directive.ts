import { LocationStrategy } from '@angular/common';
import {
  ChangeDetectorRef,
  Directive,
  Host,
  HostBinding,
  HostListener,
  Inject,
  Input,
  OnInit,
  Optional,
} from '@angular/core';
import { ActivatedRoute, Router, UrlTree } from '@angular/router';
import { Subject } from 'rxjs';
import { NAVIGABLE_COMPONENT_TOKEN, NavigableComponent } from '../../models';

@Directive({
  selector: `snf-library-side-nav-item[snfRouterLink],
             snf-library-link-button[snfRouterLink],`,
  standalone: true,
})
export class SnfRouterLinkDirective implements OnInit {
  private _href: string | null = null;

  public get urlTree(): UrlTree | null {
    if (this.routeLink === null) {
      return null;
    }
    return this.router.createUrlTree(this.routeLink, {
      relativeTo: this.route,
    });
  }

  private routeLink: string[] | null = null;

  @Input()
  public set snfRouterLink(val: string | string[]) {
    this.routeLink = typeof val === 'string' ? [val] : val;
    const oldHref = this._href;
    this._href = this.urlTree ? this.router.serializeUrl(this.urlTree) : null;
    if (this._href !== oldHref) {
      this.onChanges.next(this);
      this.updateHref();
    }
  }

  @HostBinding('attr.href') @Input() public target?: string;

  public onChanges = new Subject<SnfRouterLinkDirective>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private _locationStrategy?: LocationStrategy,
    @Optional() @Host() @Inject(NAVIGABLE_COMPONENT_TOKEN) private component?: NavigableComponent
  ) {}

  public ngOnInit(): void {
    this.updateHref();
  }

  @HostListener('click', ['$event'])
  public onClick(event: MouseEvent): boolean {
    if (
      event.button !== 0 ||
      event.ctrlKey ||
      event.shiftKey ||
      event.altKey ||
      event.metaKey ||
      (!!this.target && this.target !== '_self')
    ) {
      return true;
    }

    const urlTree = this.urlTree;
    if (urlTree) {
      this.router.navigateByUrl(urlTree);
      event.stopPropagation();
      return false;
    }

    return true;
  }

  private updateHref(): void {
    const urlTree = this.urlTree;
    if (urlTree) {
      this._href = this.router.serializeUrl(urlTree);
      if (this.component) {
        this.component.href = this._locationStrategy?.prepareExternalUrl(this._href) ?? this._href;
        this.cdr.markForCheck();
      }
    } else {
      this._href = null;
    }
  }
}
