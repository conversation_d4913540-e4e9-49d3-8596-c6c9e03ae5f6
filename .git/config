[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://<EMAIL>/SNSF-CH/SNF%20Portal/_git/library-SNF-DesignSystem
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "feature/ui-fix-for-dec"]
	remote = origin
	merge = refs/heads/feature/ui-fix-for-dec
[branch "feature/style-dictionary-v4"]
	remote = origin
	merge = refs/heads/feature/style-dictionary-v4
[branch "feature/icon-color-issue"]
	remote = origin
	merge = refs/heads/feature/icon-color-issue
[branch "feature/USP-41248-grid-global"]
	remote = origin
	merge = refs/heads/feature/USP-41248-grid-global
[branch "feature/select-button-fix"]
	remote = origin
	merge = refs/heads/feature/select-button-fix
[branch "feature/icon-colors"]
	remote = origin
	merge = refs/heads/feature/icon-colors
[branch "feature/USP-41596-status-badge"]
	remote = origin
	merge = refs/heads/feature/USP-41596-status-badge
