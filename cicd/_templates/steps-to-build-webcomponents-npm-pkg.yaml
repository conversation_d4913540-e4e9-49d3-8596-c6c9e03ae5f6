parameters:
  - name: workingDirectory
    type: string
  - name: project
    type: string

steps:
  - task: Cache@2
    inputs:
      key: 'npm | "$(Agent.OS)" | "${{ parameters.workingDirectory }}/package-lock.json"'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(npm_config_cache)
    displayName: Cache npm

  - task: npmAuthenticate@0
    displayName: '${{ parameters.project }}: NPM Auth'
    inputs:
      workingFile: ${{ parameters.workingDirectory }}/.npmrc

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Install'
    inputs:
      command: ci
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Lint'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} lint'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Analyze'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} analyze'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Check'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} check'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Test'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} test'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: PublishTestResults@2
    displayName: '${{ parameters.project }}: Publish frontend test results'
    condition: succeededOrFailed()
    inputs:
      searchFolder: '${{ parameters.workingDirectory }}/${{ parameters.project }}/test-results'
      testRunTitle: ${{ parameters.project }}
      testResultsFormat: JUnit
      testResultsFiles: TEST-vitest.xml

  - task: PublishCodeCoverageResults@2
    displayName: '${{ parameters.project }}: Publish frontend coverage results'
    condition: succeededOrFailed()
    inputs:
      summaryFileLocation: '${{ parameters.workingDirectory }}/${{ parameters.project }}/coverage/cobertura-coverage.xml'
      failIfCoverageEmpty: false

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Build'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} build'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: Write NPM Package Version'
    inputs:
      command: custom
      customCommand: 'version $(NuGetVersionV2)'
      workingDir: '${{ parameters.workingDirectory }}/${{ parameters.project }}'

  - task: PublishPipelineArtifact@1
    displayName: '${{ parameters.project }}: Publish NPM package to pipeline'
    inputs:
      targetPath: '${{ parameters.workingDirectory }}/${{ parameters.project }}'
      artifact: '${{ parameters.project }}-npm'
      publishLocation: 'pipeline'
