parameters:
  - name: stage
    type: string
  - name: serviceConnection
    type: string
  - name: webappModuleName
    type: string
  - name: artifactName
    type: string
  - name: runtime
    type: string
    default: 'NODE|18-lts'

steps:
  # Deploy Frontend AppService
  - task: AzureWebApp@1
    displayName: 'Deploy Frontend AppService'
    retryCountOnTaskFailure: 2
    timeoutInMinutes: 15
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      appType: 'webAppLinux'
      appName: 'app-snfportal-${{ parameters.webappModuleName }}-${{ parameters.stage }}'
      package: '$(PIPELINE.WORKSPACE)/${{ parameters.artifactName }}'
      runtimeStack: ${{ parameters.runtime }}
      deploymentMethod: 'runFromPackage'

  - template: 'steps-to-cleanup-frontdoor.yaml'
    parameters:
      stage: ${{ parameters.stage }}
      serviceConnection: ${{ parameters.serviceConnection }}
