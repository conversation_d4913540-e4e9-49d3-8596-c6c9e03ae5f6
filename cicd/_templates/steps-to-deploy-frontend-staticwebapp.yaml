parameters:
  - name: stage
    type: string
  - name: serviceConnection
    type: string
  - name: artifactName
    type: string
  - name: contentPath
    type: string

steps:
  # Deploy Frontend AppService
  # - task: AzureWebApp@1
  #   displayName: 'Deploy Frontend AppService'
  #   retryCountOnTaskFailure: 2
  #   timeoutInMinutes: 15
  #   inputs:
  #     azureSubscription: ${{ parameters.serviceConnection }}
  #     appType: 'webAppLinux'
  #     appName: 'app-snfportal-${{ parameters.webappModuleName }}-${{ parameters.stage }}'
  #     package: '$(PIPELINE.WORKSPACE)/${{ parameters.artifactName }}'
  #     runtimeStack: ${{ parameters.runtime }}
  #     deploymentMethod: 'runFromPackage'

    #workingDirectory: '$(System.DefaultWorkingDirectory)' # string. Alias: cwd | rootDirectory. Working directory. Default: $(System.DefaultWorkingDirectory).
    #app_location: # string. App location. 
    #app_build_command: # string. App build command. 
    #output_location: # string. Output location. 
    #api_location: # string. Api location. 
    #api_build_command: # string. Api build command. 
    #routes_location: # string. Routes location. 
    #config_file_location: # string. Config file location. 
    #skip_app_build: # boolean. Skip app build. 
    #skip_api_build: # boolean. Skip api build. 
    #is_static_export: # boolean. Set static export. 
    #verbose: # boolean. Verbose. 
    #build_timeout_in_minutes: # string. Build timeout in minutes. 
    #azure_static_web_apps_api_token: # string. Azure Static Web Apps api token. 
    #deployment_environment: # string. Deployment Environment. 
    #production_branch: # string. Production Branch.

  - task: AzureStaticWebApp@0
    displayName: 'Deploy Frontend AppService'
    inputs:
      app_location: '$(Pipeline.Workspace)/${{ parameters.artifactName }}/${{ parameters.contentPath }}'
      # output_location: '$(Pipeline.Workspace)/${{ parameters.artifactName }}/${{ parameters.contentPath }}'
      skip_app_build: true
      skip_api_build: true
      verbose: true
      azure_static_web_apps_api_token: $(deployment-token)

  - template: 'steps-to-cleanup-frontdoor.yaml'
    parameters:
      stage: ${{ parameters.stage }}
      serviceConnection: ${{ parameters.serviceConnection }}
