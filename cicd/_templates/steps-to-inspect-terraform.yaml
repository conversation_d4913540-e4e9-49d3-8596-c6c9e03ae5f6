parameters:
  - name: workingDirectory
    type: string
  - name: tfLintDirectory
    type: string
  - name: description
    type: string
    default: ''

steps:
  - script: |
      tflint --version
      tflint --init -c ${{ parameters.tfLintDirectory }}/.tflint.hcl
      tflint --call-module-type=all --color --config ${{ parameters.tfLintDirectory }}/.tflint.hcl
    workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Terraform: Linting ${{ parameters.description }}'
    env:
      GITHUB_TOKEN: "$(GITHUB_TOKEN)"