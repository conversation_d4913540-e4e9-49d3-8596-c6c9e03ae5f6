parameters:
- name: module
  type: string
- name: validateStages
  type: object
  default: ['Dev']

jobs:
  - job: "validate_infrastructure_${{ replace(parameters.module, '-', '_') }}"
    displayName: 'Infrastructure validation & publish'
    variables:
      - group: "github"
    steps:
      - checkout: self
        fetchDepth: 1
        fetchTags: false

      - ${{ if ne(variables['Build.Reason'], 'PullRequest') }}:
          - publish: '$(System.DefaultWorkingDirectory)/infrastructure'
            displayName: 'Publish infrastructure'
            artifact: '${{ parameters.module }}-infrastructure'

      - script: |
         curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
        displayName: 'Install TFLint'
        env:
          GITHUB_TOKEN: "$(GITHUB_TOKEN)"

      - ${{ each value in parameters.validateStages }}:
          - ${{ if eq(lower(value), 'dev') }}:
              - template: 'steps-to-run-terraform.yaml'
                parameters:
                  directory: '$(System.DefaultWorkingDirectory)/infrastructure/resources'
                  serviceConnection: 'PortalDev'
                  action: 'validate'
                  module: ${{ parameters.module }}
                  stage: 'resources'

          - template: 'steps-to-run-terraform.yaml'
            parameters:
              directory: '$(System.DefaultWorkingDirectory)/infrastructure/environments/${{ lower(value) }}'
              serviceConnection: 'PortalDev'
              action: 'validate'
              module: ${{ parameters.module }}
              stage: ${{ lower(value) }}