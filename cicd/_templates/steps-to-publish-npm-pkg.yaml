parameters:
  - name: workingDirectory
    type: string
  - name: project
    type: string

steps:
  - download: current
    artifact: '${{ parameters.project }}-npm'
    displayName: '${{ parameters.project }}: Download NPM package from pipeline'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Publish'
    inputs:
      command: publish
      publishRegistry: useFeed
      publishFeed: 'SNSF-CH'
      workingDir: '$(PIPELINE.WORKSPACE)/${{ parameters.project }}-npm'
