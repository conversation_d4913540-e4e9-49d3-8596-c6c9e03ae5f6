parameters:
  - name: stage
    type: string
  - name: module
    type: string
  - name: serviceConnection
    type: string
  - name: artifactName
    type: string
  - name: infrastructure_deployment_id
    type: string
    default: ''
  - name: depends_on_infrastructure_deployment_id
    type: string
    default: ''
  - name: manualCheck
    type: boolean
    default: true

jobs:
  - job: plan_infrastructure${{parameters.infrastructure_deployment_id}}
    displayName: 'Plan Infrastructure changes'
    condition: eq(${{ parameters.manualCheck }}, true)
    ${{ if ne(parameters.depends_on_infrastructure_deployment_id, '') }}:
      dependsOn: 'deploy_infrastructure${{parameters.depends_on_infrastructure_deployment_id}}'
    steps:
      - checkout: none
      - task: DownloadPipelineArtifact@2
        displayName: 'Download infrastructure artifact'
        inputs:
          artifact: ${{ parameters.artifactName }}
          path: '$(PIPELINE.WORKSPACE)/scs/infrastructure/'
      - template: 'steps-to-run-terraform.yaml'
        parameters:
          module: ${{ parameters.module }}
          serviceConnection: ${{ parameters.serviceConnection }}
          action: 'plan'
          stage: ${{ parameters.stage }}
          directory: $(PIPELINE.WORKSPACE)/scs/infrastructure/environments/${{ parameters.stage }}
          addCredentials: true

  - job: manual_validate_infrastructure${{parameters.infrastructure_deployment_id}}
    displayName: 'Manual check'
    condition: and(succeeded(), eq('${{ parameters.manualCheck }}', true), eq(dependencies.plan_infrastructure.outputs['terraformplan${{ parameters.stage }}.changesPresent'], 'true') )
    dependsOn: ['plan_infrastructure${{parameters.infrastructure_deployment_id}}', 'plan_infrastructure']
    pool: server
    steps:
      - task: ManualValidation@0
        timeoutInMinutes: 1440 # task times out in 1 day
        inputs:
            instructions: 'Please validate terraform plan to resume'
            onTimeout: 'reject'

  - deployment: 'deploy_infrastructure${{parameters.infrastructure_deployment_id}}'
    displayName: 'Deploy Infrastructure'
    condition: and(in(dependencies.manual_validate_infrastructure.result, 'Succeeded', 'Skipped'),in(dependencies.plan_infrastructure.result, 'Succeeded', 'Skipped'), or(eq(dependencies.plan_infrastructure.outputs['terraformplan${{ parameters.stage }}.changesPresent'], 'true'), eq('${{ parameters.manualCheck }}', false) ) )
    dependsOn: ['manual_validate_infrastructure${{parameters.infrastructure_deployment_id}}', 'plan_infrastructure']
    timeoutInMinutes: 180
    environment:
      name: ${{ parameters.stage }}
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: none
            - download: none
            - task: DownloadPipelineArtifact@2
              displayName: 'Download infrastructure artifact'
              inputs:
                artifact: ${{ parameters.artifactName }}
                path: '$(PIPELINE.WORKSPACE)/scs/infrastructure/'
            - template: 'steps-to-run-terraform.yaml'
              parameters:
                module: ${{ parameters.module }}
                serviceConnection: ${{ parameters.serviceConnection }}
                action: 'apply'
                stage: ${{ parameters.stage }}
                directory: $(PIPELINE.WORKSPACE)/scs/infrastructure/environments/${{ parameters.stage }}
                addCredentials: true
                ${{ if eq(parameters.manualCheck, false) }}:
                  useExistingPlan: false