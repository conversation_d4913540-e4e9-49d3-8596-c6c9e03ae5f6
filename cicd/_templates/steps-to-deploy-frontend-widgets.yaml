parameters:
  - name: stage
    type: string
  - name: serviceConnection
    type: string
  - name: storageAccount
    type: string
  - name: artifactName
    type: string
  - name: contentPath
    type: string

steps:
  # Delete Existing Files In Storage
  - task: AzureCLI@2
    displayName: 'Delete Existing Files In Storage'
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az storage blob delete-batch --source '$web' --account-name ${{ parameters.storageAccount }} --auth-mode login

  # Upload Files to Storage
  - task: AzureCLI@2
    displayName: 'Upload Files to Storage'
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az storage blob upload-batch --source "$(Pipeline.Workspace)/${{ parameters.artifactName }}/${{ parameters.contentPath }}" --destination '$web' --account-name ${{ parameters.storageAccount }} --overwrite true --auth-mode login

  - template: 'steps-to-cleanup-frontdoor.yaml'
    parameters:
      stage: ${{ parameters.stage }}
      serviceConnection: ${{ parameters.serviceConnection }}