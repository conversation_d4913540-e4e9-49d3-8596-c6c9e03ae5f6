parameters:
  - name: workingDirectory
    type: string
  - name: CodeInspection
    type: boolean
    default: true
  - name: trivyOptions
    type: string
    default: ""

steps:
- ${{ if eq(parameters.CodeInspection, true) }}:
  - task: trivy@1
    continueOnError: true
    condition: succeededOrFailed()
    displayName: "Trivy code inspection"
    inputs:
      version: 'v0.50.1'  # The documentation states that it uses the latest version if it is not defined. According to tests, however, a two-year-old version is used if it is not defined.
      docker: false
      exitCode: 0
      debug: true
      path: ${{ parameters.workingDirectory }}
      options: "${{ parameters.trivyOptions }}"