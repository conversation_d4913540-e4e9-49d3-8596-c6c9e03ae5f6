name: $(Year:yyyy).$(Date:MM).$(DayOfMonth)$(Rev:+r)

parameters:
  - name: publishPackage
    displayName: Publish NuGet package
    type: boolean
    default: false

variables:
  - group: TerraformRegistry

trigger:
  batch: true
  branches:
    include:
      - main
  paths:
    include:
      - src/*

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: Build
    variables:
      - group: 'portal-vargroup-dev'
      - template: '_templates/build-variables.yaml'
    jobs:
      - job: Build_DesignSystem
        displayName: 'Build Design System Library and Wrapper'
        steps:
          - checkout: self
            displayName: Checkout
            fetchDepth: '0'
          - template: '_templates/generate-buildnumber.yaml'
          - template: "_templates/steps-to-validate-renovate.yaml"
          - template: '_templates/steps-to-build-webcomponents-npm-pkg.yaml'
            parameters:
              workingDirectory: '$(Build.SourcesDirectory)/src'
              project: 'web-components'
          - template: '_templates/steps-to-build-angular-npm-pkg.yaml'
            parameters:
              workingDirectory: '$(Build.SourcesDirectory)/src'
              project: 'angular-wrapper'
          - template: '_templates/steps-to-build-storybook.yaml'
            parameters:
              workingDirectory: '$(Build.SourcesDirectory)/src'
              project: 'web-components'
      - template: '_templates/jobs-to-validate-infrastructure.yaml'
        parameters:
          module: 'design-system'

  - ${{ if or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(parameters.publishPackage, true)) }}:
      - stage: Publish
        variables:
          - group: 'portal-vargroup-dev'
          - template: '_templates/build-variables.yaml'
        dependsOn: [Build]
        jobs:
          - job: Publish_DesignSystem
            displayName: 'Publish DesignSystem Library'
            steps:
              - checkout: none
              - template: '_templates/steps-to-publish-npm-pkg.yaml'
                parameters:
                  workingDirectory: '$(Build.SourcesDirectory)/src'
                  project: 'web-components'
          - job: Publish_DesignSystemWrapper
            displayName: 'Publish DesignSystemWrapper Library'
            steps:
              - checkout: none
              - template: '_templates/steps-to-publish-npm-pkg.yaml'
                parameters:
                  workingDirectory: '$(Build.SourcesDirectory)/src'
                  project: 'angular-wrapper'
          - template: '_templates/jobs-to-deploy-terraform.yaml'
            parameters:
              artifactName: 'design-system-infrastructure'
              stage: dev
              module: 'design-system'
              serviceConnection: 'PortalDev'
              manualCheck: false
          - deployment: 'deploy_design_system_storybook'
            displayName: 'Deploy Design System Storybook'
            dependsOn: [deploy_infrastructure]
            condition: and(in(dependencies.deploy_infrastructure.result, 'Succeeded', 'Skipped'), in(dependencies.manual_validate_infrastructure.result, 'Succeeded', 'Skipped'), in(dependencies.plan_infrastructure.result, 'Succeeded', 'Skipped'))
            environment:
              name: 'dev'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - download: current
                      artifact: 'web-components-storybook'
                      displayName: 'Download Web Components Storybook'
                    - template: '_templates/steps-to-deploy-frontend-widgets.yaml'
                      parameters:
                        stage: dev
                        serviceConnection: 'PortalDev'
                        artifactName: 'web-components-storybook'
                        storageAccount: 'stsnfportaldswidgetsdev'
                        contentPath: ''
