variable "location" {
  type = string
}

variable "environment" {
  type = string
}

variable "tags" {
  type = map(string)
}


# the problem is that local.objectid_app_deploymentagent is defined from outside, so we still need to use that somewhere
# tflint-ignore: terraform_unused_declarations
variable "deployment_agent_id" {
  type        = string
  description = "The object id of the deployment agent"
}

