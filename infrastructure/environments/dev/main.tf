locals {
  objectid_app_deploymentagent = "13b88bc8-9134-45b8-b57d-b024baa4cc3a" # "appDisplayName": "SNSFPORTAL Azure DevOps"
}

terraform {
  backend "azurerm" {
    resource_group_name  = "rg-snfportal_deployment-dev"
    storage_account_name = "stsnfportaldepldev"
    container_name       = "tfstate"
    key                  = "design-system.tfstate"

    use_azuread_auth = true
    subscription_id  = "496f4d80-5fda-442d-b9d4-cdee87533c6e"
    tenant_id        = "d3df0f6b-32ad-4977-a262-b7727bdb4257"
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "3.117.1"
    }

    azapi = {
      source  = "Azure/azapi"
      version = "2.6.0"
    }
  }
}

provider "azurerm" {
  features {
    key_vault {
      purge_soft_delete_on_destroy = true
    }
  }

  subscription_id     = "496f4d80-5fda-442d-b9d4-cdee87533c6e"
  tenant_id           = "d3df0f6b-32ad-4977-a262-b7727bdb4257"
  storage_use_azuread = true
}

provider "azapi" {
  subscription_id = "496f4d80-5fda-442d-b9d4-cdee87533c6e"
  tenant_id       = "d3df0f6b-32ad-4977-a262-b7727bdb4257"
}

module "main" {
  source      = "../../resources"
  environment = "dev"
  location    = "Switzerland North"

  tags = {
    Service           = "SNF Portal"
    BusinessOwner     = "Emmanuel Schweizer"
    ServiceOrAppOwner = "Dirk Mutterlose"
    Environment       = "DEV"
    Autodeploy        = true
  }

  deployment_agent_id = local.objectid_app_deploymentagent
}
